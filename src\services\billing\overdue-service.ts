/**
 * Serviço para processamento de pagamentos e despesas em atraso
 * Usado pelos cron jobs para automatizar o processamento de atrasos
 */

import { createClient } from '@/services/supabase/server'

export interface OverdueProcessingResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    paymentsUpdated: number
    expensesUpdated: number
    errors: string[]
  }
}

export interface PaymentOverdueResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    updated: number
    errors: string[]
    processedPayments: Array<{
      id: string
      student_id: string
      amount: number
      due_date: string
      overdue_days: number
    }>
  }
}

export interface ExpenseOverdueResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    updated: number
    errors: string[]
    processedExpenses: Array<{
      id: string
      supplier_name: string
      amount: number
      due_date: string
      overdue_days: number
    }>
  }
}

/**
 * Calcula quantos dias um pagamento/despesa está em atraso
 */
function calculateOverdueDays(dueDate: string): number {
  const due = new Date(dueDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  due.setHours(0, 0, 0, 0)
  
  const diffTime = today.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return Math.max(0, diffDays)
}

/**
 * Processa pagamentos em atraso
 * Atualiza status para 'overdue' e define overdue_date
 */
export async function processOverduePayments(tenantId?: string): Promise<PaymentOverdueResult> {
  try {
    const supabase = await createClient()
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD

    console.log(`🔄 Processando pagamentos em atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)
    console.log(`📅 Data de hoje para comparação: ${today}`)

    // Buscar pagamentos pendentes com data de vencimento passada
    let query = supabase
      .from('payments')
      .select(`
        id,
        tenant_id,
        student_id,
        amount,
        due_date,
        status,
        overdue_date,
        students!inner (
          id,
          user_id,
          users!students_user_id_fkey (
            first_name,
            last_name,
            email
          )
        )
      `)
      .eq('status', 'pending')
      .lt('due_date', today)
      .is('overdue_date', null)

    if (tenantId) {
      query = query.eq('tenant_id', tenantId)
      console.log(`🏢 Filtrando por tenant: ${tenantId}`)
    }

    console.log(`🔍 Executando query para buscar pagamentos em atraso...`)
    const { data: overduePayments, error: fetchError } = await query

    if (fetchError) {
      console.error('❌ Erro ao buscar pagamentos em atraso:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar pagamentos: ${fetchError.message}`
      }
    }

    console.log(`📊 Query executada. Resultados encontrados: ${overduePayments?.length || 0}`)

    if (overduePayments && overduePayments.length > 0) {
      console.log('🔍 Pagamentos encontrados:')
      overduePayments.forEach(payment => {
        console.log(`  - ID: ${payment.id}, Due: ${payment.due_date}, Status: ${payment.status}, Overdue: ${payment.overdue_date}`)
      })
    }

    if (!overduePayments || overduePayments.length === 0) {
      console.log('✅ Nenhum pagamento em atraso encontrado')

      // Vamos fazer uma query de debug para entender por que não encontrou
      console.log('🔍 Debug: Verificando pagamentos pending...')
      const { data: debugPayments } = await supabase
        .from('payments')
        .select('id, due_date, status, overdue_date')
        .eq('status', 'pending')
        .is('overdue_date', null)

      console.log(`📊 Debug: Total de pagamentos pending: ${debugPayments?.length || 0}`)
      if (debugPayments && debugPayments.length > 0) {
        debugPayments.forEach(payment => {
          console.log(`  - ID: ${payment.id}, Due: ${payment.due_date}, Status: ${payment.status}`)
        })
      }

      return {
        success: true,
        data: {
          totalProcessed: 0,
          updated: 0,
          errors: [],
          processedPayments: []
        }
      }
    }

    console.log(`📋 Encontrados ${overduePayments.length} pagamentos em atraso`)

    const errors: string[] = []
    const processedPayments: Array<{
      id: string
      student_id: string
      amount: number
      due_date: string
      overdue_days: number
    }> = []

    // Processar cada pagamento em atraso
    for (const payment of overduePayments) {
      try {
        const overdueDays = calculateOverdueDays(payment.due_date)
        
        // Atualizar status para overdue e definir overdue_date
        const { error: updateError } = await supabase
          .from('payments')
          .update({
            status: 'overdue',
            overdue_date: today,
            updated_at: new Date().toISOString()
          })
          .eq('id', payment.id)

        if (updateError) {
          const errorMsg = `Erro ao atualizar pagamento ${payment.id}: ${updateError.message}`
          console.error('❌', errorMsg)
          errors.push(errorMsg)
          continue
        }

        processedPayments.push({
          id: payment.id,
          student_id: payment.student_id,
          amount: parseFloat(payment.amount),
          due_date: payment.due_date,
          overdue_days: overdueDays
        })

        console.log(`✅ Pagamento ${payment.id} marcado como em atraso (${overdueDays} dias)`)

      } catch (error) {
        const errorMsg = `Erro ao processar pagamento ${payment.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    const result = {
      totalProcessed: overduePayments.length,
      updated: processedPayments.length,
      errors,
      processedPayments
    }

    console.log(`✅ Processamento concluído: ${result.updated}/${result.totalProcessed} pagamentos atualizados`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar pagamentos em atraso:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa despesas em atraso
 * Atualiza status para 'overdue'
 */
export async function processOverdueExpenses(tenantId?: string): Promise<ExpenseOverdueResult> {
  try {
    const supabase = await createClient()
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD
    
    console.log(`🔄 Processando despesas em atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Buscar despesas pendentes com data de vencimento passada
    let query = supabase
      .from('expenses')
      .select(`
        id,
        tenant_id,
        supplier_name,
        amount,
        due_date,
        status
      `)
      .eq('status', 'pending')
      .lt('due_date', today)

    if (tenantId) {
      query = query.eq('tenant_id', tenantId)
    }

    const { data: overdueExpenses, error: fetchError } = await query

    if (fetchError) {
      console.error('❌ Erro ao buscar despesas em atraso:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar despesas: ${fetchError.message}`
      }
    }

    if (!overdueExpenses || overdueExpenses.length === 0) {
      console.log('✅ Nenhuma despesa em atraso encontrada')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          updated: 0,
          errors: [],
          processedExpenses: []
        }
      }
    }

    console.log(`📋 Encontradas ${overdueExpenses.length} despesas em atraso`)

    const errors: string[] = []
    const processedExpenses: Array<{
      id: string
      supplier_name: string
      amount: number
      due_date: string
      overdue_days: number
    }> = []

    // Processar cada despesa em atraso
    for (const expense of overdueExpenses) {
      try {
        const overdueDays = calculateOverdueDays(expense.due_date)
        
        // Atualizar status para overdue
        const { error: updateError } = await supabase
          .from('expenses')
          .update({
            status: 'overdue',
            updated_at: new Date().toISOString()
          })
          .eq('id', expense.id)

        if (updateError) {
          const errorMsg = `Erro ao atualizar despesa ${expense.id}: ${updateError.message}`
          console.error('❌', errorMsg)
          errors.push(errorMsg)
          continue
        }

        processedExpenses.push({
          id: expense.id,
          supplier_name: expense.supplier_name,
          amount: parseFloat(expense.amount),
          due_date: expense.due_date,
          overdue_days: overdueDays
        })

        console.log(`✅ Despesa ${expense.id} marcada como em atraso (${overdueDays} dias)`)

      } catch (error) {
        const errorMsg = `Erro ao processar despesa ${expense.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    const result = {
      totalProcessed: overdueExpenses.length,
      updated: processedExpenses.length,
      errors,
      processedExpenses
    }

    console.log(`✅ Processamento concluído: ${result.updated}/${result.totalProcessed} despesas atualizadas`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar despesas em atraso:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa tanto pagamentos quanto despesas em atraso
 */
export async function processAllOverdue(tenantId?: string): Promise<OverdueProcessingResult> {
  try {
    console.log(`🚀 Iniciando processamento completo de atrasos...`)

    const [paymentsResult, expensesResult] = await Promise.all([
      processOverduePayments(tenantId),
      processOverdueExpenses(tenantId)
    ])

    const errors: string[] = []
    
    if (!paymentsResult.success) {
      errors.push(`Pagamentos: ${paymentsResult.error}`)
    }
    
    if (!expensesResult.success) {
      errors.push(`Despesas: ${expensesResult.error}`)
    }

    // Combinar erros dos processamentos individuais
    if (paymentsResult.data?.errors) {
      errors.push(...paymentsResult.data.errors)
    }
    
    if (expensesResult.data?.errors) {
      errors.push(...expensesResult.data.errors)
    }

    const totalProcessed = (paymentsResult.data?.totalProcessed || 0) + (expensesResult.data?.totalProcessed || 0)
    const paymentsUpdated = paymentsResult.data?.updated || 0
    const expensesUpdated = expensesResult.data?.updated || 0

    console.log(`✅ Processamento completo finalizado:`)
    console.log(`  - Total processados: ${totalProcessed}`)
    console.log(`  - Pagamentos atualizados: ${paymentsUpdated}`)
    console.log(`  - Despesas atualizadas: ${expensesUpdated}`)
    console.log(`  - Erros: ${errors.length}`)

    return {
      success: paymentsResult.success && expensesResult.success,
      data: {
        totalProcessed,
        paymentsUpdated,
        expensesUpdated,
        errors
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico no processamento completo:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}
