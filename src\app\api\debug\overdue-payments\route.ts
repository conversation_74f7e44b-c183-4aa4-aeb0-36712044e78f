import { NextRequest, NextResponse } from 'next/server';
import { processOverduePayments } from '@/services/billing/overdue-service';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug: Executando processOverduePayments...');
    
    const result = await processOverduePayments();
    
    console.log('📊 Debug: Resultado do processamento:', JSON.stringify(result, null, 2));
    
    return NextResponse.json({
      debug: true,
      timestamp: new Date().toISOString(),
      result
    });
    
  } catch (error) {
    console.error('💥 Debug: Erro crítico:', error);
    
    return NextResponse.json({
      debug: true,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
