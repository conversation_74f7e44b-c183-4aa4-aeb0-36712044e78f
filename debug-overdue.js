// Script para debugar o processamento de pagamentos em atraso
import { processOverduePayments } from './src/services/billing/overdue-service.js';

async function debugOverduePayments() {
  console.log('🔍 Iniciando debug do processamento de pagamentos em atraso...');
  
  try {
    const result = await processOverduePayments();
    
    console.log('📊 Resultado do processamento:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success && result.data) {
      console.log(`\n✅ Processamento concluído:`);
      console.log(`  - Total processados: ${result.data.totalProcessed}`);
      console.log(`  - Atualizados: ${result.data.updated}`);
      console.log(`  - Erros: ${result.data.errors.length}`);
      
      if (result.data.processedPayments.length > 0) {
        console.log('\n📋 Pagamentos processados:');
        result.data.processedPayments.forEach(payment => {
          console.log(`  - ID: ${payment.id}, Dias em atraso: ${payment.overdue_days}`);
        });
      }
      
      if (result.data.errors.length > 0) {
        console.log('\n❌ Erros encontrados:');
        result.data.errors.forEach(error => {
          console.log(`  - ${error}`);
        });
      }
    } else {
      console.log('❌ Falha no processamento:', result.error);
    }
    
  } catch (error) {
    console.error('💥 Erro crítico:', error);
  }
}

debugOverduePayments();
